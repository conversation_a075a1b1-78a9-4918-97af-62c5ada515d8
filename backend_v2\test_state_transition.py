#!/usr/bin/env python3
"""
测试任务状态转换功能
验证2.1.2.3的实现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.task import TaskStatus
from app.models.batch import BatchStatus
from app.services.task_state_transition import (
    TaskStateTransitionService, 
    BatchStateTransitionService,
    StateTransitionError
)

def test_task_state_transitions():
    """测试任务状态转换规则"""
    print("🧪 测试任务状态转换规则...")
    
    # 测试允许的转换
    valid_transitions = [
        (TaskStatus.PENDING, TaskStatus.IN_PROGRESS),
        (TaskStatus.IN_PROGRESS, TaskStatus.PAUSED),
        (TaskStatus.PAUSED, TaskStatus.IN_PROGRESS),
        (TaskStatus.IN_PROGRESS, TaskStatus.CANCELLING),
        (TaskStatus.CANCELLING, TaskStatus.CANCELLED),
        (TaskStatus.IN_PROGRESS, TaskStatus.SUCCESS),
        (TaskStatus.IN_PROGRESS, TaskStatus.FAILED),
        (TaskStatus.PENDING, TaskStatus.CANCELLED)
    ]
    
    for current, target in valid_transitions:
        is_valid, error_msg = TaskStateTransitionService.validate_transition(current, target)
        assert is_valid, f"转换 {current.value} -> {target.value} 应该被允许，但被拒绝: {error_msg}"
        print(f"✅ {current.value} -> {target.value}")
    
    # 测试不允许的转换
    invalid_transitions = [
        (TaskStatus.SUCCESS, TaskStatus.IN_PROGRESS),
        (TaskStatus.FAILED, TaskStatus.PENDING),
        (TaskStatus.CANCELLED, TaskStatus.IN_PROGRESS),
        (TaskStatus.PENDING, TaskStatus.PAUSED),
        (TaskStatus.PENDING, TaskStatus.SUCCESS)
    ]
    
    for current, target in invalid_transitions:
        is_valid, error_msg = TaskStateTransitionService.validate_transition(current, target)
        assert not is_valid, f"转换 {current.value} -> {target.value} 应该被拒绝，但被允许"
        print(f"❌ {current.value} -> {target.value} (正确拒绝)")
    
    print("✅ 任务状态转换规则验证通过")

def test_task_state_helpers():
    """测试任务状态辅助方法"""
    print("\n🧪 测试任务状态辅助方法...")
    
    # 测试终态检查
    terminal_statuses = [TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.CANCELLED, TaskStatus.PARTIAL]
    for status in terminal_statuses:
        assert TaskStateTransitionService.is_terminal_status(status), f"{status.value} 应该是终态"
        print(f"✅ {status.value} 是终态")
    
    # 测试活跃状态检查
    active_statuses = [TaskStatus.PENDING, TaskStatus.IN_PROGRESS]
    for status in active_statuses:
        assert TaskStateTransitionService.is_active_status(status), f"{status.value} 应该是活跃状态"
        print(f"✅ {status.value} 是活跃状态")
    
    # 测试操作权限检查
    assert TaskStateTransitionService.can_be_paused(TaskStatus.IN_PROGRESS), "IN_PROGRESS 应该可以暂停"
    assert not TaskStateTransitionService.can_be_paused(TaskStatus.PENDING), "PENDING 不应该可以暂停"
    
    assert TaskStateTransitionService.can_be_resumed(TaskStatus.PAUSED), "PAUSED 应该可以恢复"
    assert not TaskStateTransitionService.can_be_resumed(TaskStatus.IN_PROGRESS), "IN_PROGRESS 不应该可以恢复"
    
    assert TaskStateTransitionService.can_be_cancelled(TaskStatus.IN_PROGRESS), "IN_PROGRESS 应该可以取消"
    assert not TaskStateTransitionService.can_be_cancelled(TaskStatus.SUCCESS), "SUCCESS 不应该可以取消"
    
    print("✅ 任务状态辅助方法验证通过")

def test_batch_state_transitions():
    """测试批次状态转换规则"""
    print("\n🧪 测试批次状态转换规则...")
    
    # 测试允许的转换
    valid_transitions = [
        (BatchStatus.PENDING, BatchStatus.IN_PROGRESS),
        (BatchStatus.IN_PROGRESS, BatchStatus.SUCCESS),
        (BatchStatus.IN_PROGRESS, BatchStatus.FAILED),
        (BatchStatus.PENDING, BatchStatus.ABORTED)
    ]
    
    for current, target in valid_transitions:
        is_valid, error_msg = BatchStateTransitionService.validate_transition(current, target)
        assert is_valid, f"批次转换 {current.value} -> {target.value} 应该被允许，但被拒绝: {error_msg}"
        print(f"✅ {current.value} -> {target.value}")
    
    # 测试不允许的转换
    invalid_transitions = [
        (BatchStatus.SUCCESS, BatchStatus.IN_PROGRESS),
        (BatchStatus.FAILED, BatchStatus.PENDING),
        (BatchStatus.ABORTED, BatchStatus.IN_PROGRESS)
    ]
    
    for current, target in invalid_transitions:
        is_valid, error_msg = BatchStateTransitionService.validate_transition(current, target)
        assert not is_valid, f"批次转换 {current.value} -> {target.value} 应该被拒绝，但被允许"
        print(f"❌ {current.value} -> {target.value} (正确拒绝)")
    
    print("✅ 批次状态转换规则验证通过")

def test_transition_log_creation():
    """测试转换日志创建"""
    print("\n🧪 测试转换日志创建...")
    
    # 创建转换日志
    log = TaskStateTransitionService.create_transition_log(
        task_id=123,
        current_status=TaskStatus.PENDING,
        target_status=TaskStatus.IN_PROGRESS,
        reason="开始处理任务",
        user_id=456
    )
    
    # 验证日志结构
    assert log["task_id"] == 123
    assert log["from_status"] == "pending"
    assert log["to_status"] == "in_progress"
    assert log["reason"] == "开始处理任务"
    assert log["user_id"] == 456
    assert log["transition_type"] == "manual"
    assert "timestamp" in log
    
    print(f"✅ 转换日志创建成功: {log}")
    
    # 测试自动转换日志
    auto_log = TaskStateTransitionService.create_transition_log(
        task_id=123,
        current_status=TaskStatus.IN_PROGRESS,
        target_status=TaskStatus.SUCCESS
    )
    
    assert auto_log["transition_type"] == "automatic"
    assert auto_log["user_id"] is None
    
    print("✅ 转换日志创建验证通过")

def test_transition_reasons():
    """测试转换原因描述"""
    print("\n🧪 测试转换原因描述...")
    
    test_cases = [
        (TaskStatus.PENDING, TaskStatus.IN_PROGRESS, "开始处理任务"),
        (TaskStatus.IN_PROGRESS, TaskStatus.PAUSED, "用户暂停任务"),
        (TaskStatus.PAUSED, TaskStatus.IN_PROGRESS, "用户恢复任务"),
        (TaskStatus.IN_PROGRESS, TaskStatus.SUCCESS, "任务处理成功")
    ]
    
    for current, target, expected_reason in test_cases:
        reason = TaskStateTransitionService.get_transition_reason(current, target)
        assert reason == expected_reason, f"转换原因不匹配: 期望 '{expected_reason}', 实际 '{reason}'"
        print(f"✅ {current.value} -> {target.value}: {reason}")
    
    print("✅ 转换原因描述验证通过")

def test_allowed_transitions():
    """测试获取允许的转换"""
    print("\n🧪 测试获取允许的转换...")
    
    # 测试IN_PROGRESS状态的允许转换
    allowed = TaskStateTransitionService.get_allowed_transitions(TaskStatus.IN_PROGRESS)
    expected = [TaskStatus.PAUSED, TaskStatus.CANCELLING, TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.PARTIAL]
    
    assert set(allowed) == set(expected), f"IN_PROGRESS允许的转换不匹配"
    print(f"✅ IN_PROGRESS 允许转换到: {[s.value for s in allowed]}")
    
    # 测试终态的允许转换
    terminal_allowed = TaskStateTransitionService.get_allowed_transitions(TaskStatus.SUCCESS)
    assert terminal_allowed == [], "终态不应该允许任何转换"
    print("✅ 终态不允许转换")
    
    print("✅ 允许转换获取验证通过")

if __name__ == "__main__":
    print("🚀 开始测试任务2.1.2.3的实现...")
    
    try:
        # 运行所有测试
        test_task_state_transitions()
        test_task_state_helpers()
        test_batch_state_transitions()
        test_transition_log_creation()
        test_transition_reasons()
        test_allowed_transitions()
        
        print("\n🎉 所有测试通过！")
        print("✅ 任务2.1.2.3 - 添加任务状态转换的业务逻辑 验证完成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
