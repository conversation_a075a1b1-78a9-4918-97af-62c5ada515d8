#!/usr/bin/env python3
"""
测试任务创建功能
验证2.1.2.2的实现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.schemas.task_submit import TaskSubmitRequest, TaskType, FileInput
from app.core.database import get_db
from app.crud.crud_task import task as crud_task
from app.crud.crud_batch import batch as crud_batch
from app.crud.crud_file import file as crud_file

def test_task_creation():
    """测试任务创建功能"""
    print("🧪 测试任务创建功能...")
    
    # 创建测试请求
    test_request = TaskSubmitRequest(
        name="测试图像锐化任务",
        task_type=TaskType.IMAGE_SHARPEN,
        files=[
            FileInput(
                file_id="file_001",
                filename="test1.jpg",
                size=1024000,
                minio_path="uploads/test1.jpg"
            ),
            FileInput(
                file_id="file_002",
                filename="test2.jpg",
                size=2048000,
                minio_path="uploads/test2.jpg"
            ),
            FileInput(
                file_id="file_003",
                filename="test3.jpg",
                size=1536000,
                minio_path="uploads/test3.jpg"
            )
        ],
        parameters={
            "kernel_size": 3,
            "sigma": 1.0,
            "amount": 1.5
        }
    )
    
    print(f"✅ 创建测试请求: {test_request.name}")
    print(f"   - 任务类型: {test_request.task_type}")
    print(f"   - 文件数量: {len(test_request.files)}")
    print(f"   - 参数: {test_request.parameters}")
    
    # 验证请求结构
    assert test_request.name == "测试图像锐化任务"
    assert test_request.task_type == TaskType.IMAGE_SHARPEN
    assert len(test_request.files) == 3
    
    print("✅ 请求结构验证通过")
    
    # 测试数据库连接
    try:
        db = next(get_db())
        print("✅ 数据库连接成功")
        
        # 测试CRUD操作可用性
        tasks = crud_task.get_multi(db=db, limit=1)
        print(f"✅ CRUD操作可用，当前任务数: {len(tasks)}")
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    
    print("🎯 任务创建功能基础验证完成")
    return True

def test_file_type_detection():
    """测试文件类型检测"""
    print("\n🧪 测试文件类型检测...")
    
    test_cases = [
        ("image.jpg", "IMAGE"),
        ("video.mp4", "VIDEO"),
        ("document.pdf", "OTHER"),
        ("test.png", "IMAGE"),
        ("movie.avi", "VIDEO")
    ]
    
    for filename, expected_type in test_cases:
        # 模拟文件类型检测逻辑
        file_extension = filename.split('.')[-1].lower() if '.' in filename else ""
        if file_extension in ["jpg", "jpeg", "png", "gif", "bmp", "tiff", "webp"]:
            detected_type = "IMAGE"
        elif file_extension in ["mp4", "avi", "mov", "mkv", "wmv", "flv"]:
            detected_type = "VIDEO"
        else:
            detected_type = "OTHER"
        
        assert detected_type == expected_type, f"文件 {filename} 类型检测失败"
        print(f"✅ {filename} -> {detected_type}")
    
    print("✅ 文件类型检测验证通过")

def test_batch_strategy():
    """测试批次策略"""
    print("\n🧪 测试批次策略...")
    
    # 模拟批次策略计算
    file_count = 10
    estimated_batches = 3
    files_per_batch = file_count // estimated_batches
    
    batches = []
    for i in range(estimated_batches):
        batch_files = files_per_batch
        if i == estimated_batches - 1:  # 最后一个批次包含剩余文件
            batch_files = file_count - (i * files_per_batch)
        batches.append(batch_files)
    
    print(f"✅ 文件分配: {batches}")
    assert sum(batches) == file_count, "文件分配总数不匹配"
    assert len(batches) == estimated_batches, "批次数量不匹配"
    
    print("✅ 批次策略验证通过")

if __name__ == "__main__":
    print("🚀 开始测试任务2.1.2.2的实现...")
    
    try:
        # 运行所有测试
        test_task_creation()
        test_file_type_detection()
        test_batch_strategy()
        
        print("\n🎉 所有测试通过！")
        print("✅ 任务2.1.2.2 - 实现任务创建的CRUD操作 基础验证完成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)
