"""
任务管理API
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
import uuid
import json
import structlog
from datetime import datetime

from app.core.database import get_db
from app.schemas.task_submit import (
    TaskSubmitRequest, TaskSubmitResponse, TaskSubmitError,
    TaskType, QueueType, BatchInfo, COMMON_ERRORS
)
from app.schemas.task import TaskCreate
from app.schemas.batch import BatchCreate
from app.schemas.file import FileCreate
from app.models.file import FileType, FileStatus
from app.models.batch import BatchStatus
from app.models.task import TaskStatus
from app.crud.crud_task import task as crud_task
from app.crud.crud_batch import batch as crud_batch
from app.crud.crud_file import file as crud_file
from app.services.queue_router import QueueRouter
from app.services.parameter_validator import ParameterValidator
from app.services.task_management import TaskManagementService
from app.services.task_state_transition import StateTransitionError
from app.services.task_persistence import TaskPersistenceService, TaskPersistenceError
from app.services.task_dispatcher import TaskDispatcher, TaskDispatchError

logger = structlog.get_logger()
router = APIRouter(prefix="/tasks", tags=["Tasks"])


# WebSocket连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.task_connections: dict = {}  # task_id -> [websockets]

    async def connect(self, websocket: WebSocket, task_id: str = None):
        await websocket.accept()
        self.active_connections.append(websocket)
        if task_id:
            if task_id not in self.task_connections:
                self.task_connections[task_id] = []
            self.task_connections[task_id].append(websocket)

    def disconnect(self, websocket: WebSocket, task_id: str = None):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if task_id and task_id in self.task_connections:
            if websocket in self.task_connections[task_id]:
                self.task_connections[task_id].remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def send_task_update(self, task_id: str, message: dict):
        if task_id in self.task_connections:
            for connection in self.task_connections[task_id]:
                try:
                    await connection.send_text(json.dumps(message))
                except:
                    # 连接已断开，移除
                    self.task_connections[task_id].remove(connection)


manager = ConnectionManager()


@router.post("/", response_model=TaskSubmitResponse)
async def submit_task(
    request: TaskSubmitRequest,
    db: Session = Depends(get_db)
) -> TaskSubmitResponse:
    """
    提交处理任务

    支持的任务类型：
    - 图像处理：锐化、灰度、边缘检测、伽马校正、融合、拼接、美颜、纹理转换
    - 视频处理：缩放、灰度、帧提取、边缘检测、模糊、二值化、变换、缩略图

    处理流程：
    1. 参数验证和任务类型检查
    2. 队列路由决策和批次规划
    3. 创建Task和Batch记录
    4. 提交Celery任务
    5. 返回任务信息和状态

    Args:
        request: 任务提交请求
        db: 数据库会话

    Returns:
        任务提交响应，包含任务ID、状态、批次信息等

    Raises:
        HTTPException: 当请求无效或系统错误时
    """
    try:
        logger.info(
            "task_submit_request",
            task_type=request.task_type,
            file_count=len(request.files),
            name=request.name
        )

        # 1. 参数验证
        is_valid, error_msg, validated_params = ParameterValidator.validate_task_submission(
            request.task_type,
            request.files,
            request.parameters
        )

        if not is_valid:
            raise ValueError(error_msg)

        # 2. 获取参数警告
        warnings = ParameterValidator.validate_parameter_ranges(
            request.task_type,
            validated_params
        )

        if warnings:
            logger.info(
                "task_parameter_warnings",
                task_type=request.task_type,
                warnings=warnings
            )

        # 3. 队列路由和批次策略
        batch_strategy = QueueRouter.get_batch_strategy(
            request.task_type,
            request.files
        )

        # 4. 估算执行时间
        estimated_duration = QueueRouter.estimate_duration(
            request.task_type,
            request.files
        )

        # 5. 处理复杂度评估
        complexity = ParameterValidator.estimate_processing_complexity(
            request.task_type,
            request.files,
            validated_params
        )

        # 3. 创建数据库记录
        try:
            # 准备任务数据
            task_create = TaskCreate(
                name=request.name,
                description=f"处理任务: {request.task_type.value}",
                config={
                    "task_type": request.task_type.value,
                    "parameters": validated_params,
                    "batch_strategy": batch_strategy,
                    "complexity": complexity
                }
            )

            # 准备批次数据
            batch_count = batch_strategy["estimated_batches"]
            files_per_batch = len(request.files) // batch_count
            batches_data = []

            for i in range(batch_count):
                batch_create = BatchCreate(
                    task_id=0,  # 将在持久化服务中设置
                    name=f"{request.name}_batch_{i+1}",
                    description=f"批次 {i+1}/{batch_count}",
                    operation_type=request.task_type.value,
                    queue_name=batch_strategy["queue_type"].value,
                    config={
                        "batch_index": i + 1,
                        "total_batches": batch_count,
                        "estimated_duration": estimated_duration // batch_count
                    }
                )
                batches_data.append(batch_create)

            # 准备文件数据
            files_data = []
            for i, file_input in enumerate(request.files):
                # 确定文件所属的批次索引
                batch_index = i // files_per_batch
                if batch_index >= batch_count:
                    batch_index = batch_count - 1

                # 确定文件类型
                file_extension = file_input.filename.split('.')[-1].lower() if '.' in file_input.filename else ""
                if file_extension in ["jpg", "jpeg", "png", "gif", "bmp", "tiff", "webp"]:
                    file_type = FileType.IMAGE
                elif file_extension in ["mp4", "avi", "mov", "mkv", "wmv", "flv"]:
                    file_type = FileType.VIDEO
                else:
                    file_type = FileType.OTHER

                file_create = FileCreate(
                    batch_id=batch_index,  # 临时使用索引，持久化服务中会替换为真实ID
                    filename=file_input.filename,
                    original_filename=file_input.filename,
                    file_type=file_type,
                    content_type=f"image/{file_extension}" if file_type == FileType.IMAGE else f"video/{file_extension}" if file_type == FileType.VIDEO else "application/octet-stream",
                    storage_path=file_input.minio_path,
                    storage_bucket="clover-files",
                    file_size=file_input.size,
                    file_metadata={
                        "original_name": file_input.filename,
                        "upload_size": file_input.size,
                        "task_type": request.task_type.value,
                        "file_id": file_input.file_id
                    }
                )
                files_data.append(file_create)

            # 使用持久化服务创建完整任务
            created_task, created_batches, created_files = TaskPersistenceService.create_complete_task(
                db=db,
                task_data=task_create,
                batches_data=batches_data,
                files_data=files_data
            )

            # 4. 提交Celery任务
            try:
                celery_job_ids = TaskDispatcher.dispatch_task(
                    db=db,
                    task_id=int(created_task.id),
                    task_type=request.task_type,
                    queue_type=batch_strategy["queue_type"],
                    parameters=validated_params
                )
                logger.info("celery_tasks_dispatched", task_id=created_task.id, job_ids=celery_job_ids)
            except TaskDispatchError as e:
                logger.error("task_dispatch_failed", task_id=created_task.id, error=str(e))
                # 任务分发失败，但任务已创建，返回警告
                celery_job_ids = []
                warnings.append(f"任务分发失败: {str(e)}")
            except Exception as e:
                logger.error("task_dispatch_unexpected_error", task_id=created_task.id, error=str(e))
                celery_job_ids = []
                warnings.append(f"任务分发异常: {str(e)}")

        except TaskPersistenceError as e:
            logger.error("task_creation_persistence_error", error=str(e))
            raise ValueError(str(e))
        except Exception as e:
            logger.error("task_creation_unexpected_error", error=str(e))
            raise ValueError(f"创建任务记录失败: {str(e)}")

        # 构建批次信息
        batches = []
        for batch in created_batches:
            batch_file_count = sum(1 for f in created_files if int(f.batch_id) == int(batch.id))
            batches.append(BatchInfo(
                batch_id=int(batch.id),
                queue_type=QueueType(str(batch.queue_name)),
                file_count=batch_file_count,
                estimated_duration=estimated_duration // len(created_batches)
            ))

        response = TaskSubmitResponse(
            task_id=int(created_task.id),
            status=str(created_task.status.value),
            name=str(created_task.name),
            task_type=request.task_type,
            created_at=created_task.created_at,
            batch_count=len(created_batches),
            batches=batches,
            total_files=len(created_files),
            estimated_total_duration=estimated_duration,
            celery_job_ids=celery_job_ids
        )

        logger.info(
            "task_submit_success",
            task_id=response.task_id,
            batch_count=response.batch_count,
            queue_type=batch_strategy["queue_type"],
            complexity=complexity,
            warnings_count=len(warnings),
            files_created=len(created_files),
            batches_created=len(created_batches)
        )

        return response

    except ValueError as e:
        logger.error("task_submit_validation_error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=TaskSubmitError(
                error_code="VALIDATION_ERROR",
                error_message=str(e)
            ).dict()
        )
    except Exception as e:
        logger.error("task_submit_system_error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=TaskSubmitError(
                error_code="SYSTEM_ERROR",
                error_message="系统内部错误，请稍后重试"
            ).dict()
        )


@router.get("/")
async def list_tasks(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取任务列表
    """
    try:
        # 根据状态过滤任务
        if status:
            try:
                task_status = TaskStatus(status)
                tasks = crud_task.get_by_status(db=db, status=task_status, skip=skip, limit=limit)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的任务状态: {status}"
                )
        else:
            tasks = crud_task.get_multi(db=db, skip=skip, limit=limit)

        # 构建响应数据
        task_list = []
        for task in tasks:
            # 获取任务的文件数量
            total_files = 0
            for batch in task.batches:
                total_files += batch.total_files

            task_list.append({
                "id": task.id,
                "name": task.name,
                "status": task.status.value,
                "progress": task.progress,
                "created_at": task.created_at.isoformat(),
                "updated_at": task.updated_at.isoformat(),
                "total_batches": task.total_batches,
                "completed_batches": task.completed_batches,
                "failed_batches": task.failed_batches,
                "files_count": total_files
            })

        return {
            "tasks": task_list,
            "total": len(task_list),
            "skip": skip,
            "limit": limit
        }

    except Exception as e:
        logger.error("list_tasks_error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务列表失败"
        )


@router.get("/{task_id}")
async def get_task(
    task_id: int,
    db: Session = Depends(get_db)
):
    """
    获取任务详情
    """
    try:
        # 使用持久化服务获取任务详情
        task_details = TaskPersistenceService.get_task_with_details(
            db=db,
            task_id=task_id,
            include_files=True
        )

        if not task_details:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务 {task_id} 不存在"
            )

        # 添加允许的操作
        allowed_actions = TaskManagementService.get_task_allowed_actions(db=db, task_id=task_id)
        task_details["allowed_actions"] = allowed_actions

        return task_details

    except HTTPException:
        raise
    except Exception as e:
        logger.error("get_task_error", task_id=task_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务详情失败"
        )


@router.post("/{task_id}/pause")
async def pause_task(
    task_id: int,
    db: Session = Depends(get_db)
):
    """
    暂停任务
    """
    try:
        # 使用状态转换服务暂停任务
        updated_task = TaskManagementService.pause_task(
            db=db,
            task_id=task_id,
            user_id=None  # TODO: 从认证中获取用户ID
        )

        # TODO: 暂停Celery任务 (需要在后续任务2.1.3中实现)

        logger.info("task_paused", task_id=task_id)
        return {
            "message": f"任务 {task_id} 已暂停",
            "task_id": task_id,
            "status": updated_task.status.value,
            "allowed_actions": TaskManagementService.get_task_allowed_actions(db=db, task_id=task_id)
        }

    except StateTransitionError as e:
        logger.warning("pause_task_invalid_transition", task_id=task_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("pause_task_error", task_id=task_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="暂停任务失败"
        )


@router.post("/{task_id}/resume")
async def resume_task(
    task_id: int,
    db: Session = Depends(get_db)
):
    """
    恢复任务
    """
    try:
        # 使用状态转换服务恢复任务
        updated_task = TaskManagementService.resume_task(
            db=db,
            task_id=task_id,
            user_id=None  # TODO: 从认证中获取用户ID
        )

        # TODO: 恢复Celery任务 (需要在后续任务2.1.3中实现)

        logger.info("task_resumed", task_id=task_id)
        return {
            "message": f"任务 {task_id} 已恢复",
            "task_id": task_id,
            "status": updated_task.status.value,
            "allowed_actions": TaskManagementService.get_task_allowed_actions(db=db, task_id=task_id)
        }

    except StateTransitionError as e:
        logger.warning("resume_task_invalid_transition", task_id=task_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("resume_task_error", task_id=task_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="恢复任务失败"
        )


@router.post("/{task_id}/cancel")
async def cancel_task(
    task_id: int,
    db: Session = Depends(get_db)
):
    """
    取消任务
    """
    try:
        # 使用状态转换服务取消任务
        updated_task = TaskManagementService.cancel_task(
            db=db,
            task_id=task_id,
            user_id=None  # TODO: 从认证中获取用户ID
        )

        # TODO: 取消Celery任务 (需要在后续任务2.1.3中实现)

        logger.info("task_cancelled", task_id=task_id)
        return {
            "message": f"任务 {task_id} 已取消",
            "task_id": task_id,
            "status": updated_task.status.value,
            "allowed_actions": TaskManagementService.get_task_allowed_actions(db=db, task_id=task_id)
        }

    except StateTransitionError as e:
        logger.warning("cancel_task_invalid_transition", task_id=task_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("cancel_task_error", task_id=task_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="取消任务失败"
        )


@router.get("/{task_id}/status-history")
async def get_task_status_history(
    task_id: int,
    db: Session = Depends(get_db)
):
    """
    获取任务状态转换历史
    """
    try:
        # 检查任务是否存在
        task = crud_task.get(db=db, id=task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务 {task_id} 不存在"
            )

        # 获取状态历史
        status_history = TaskManagementService.get_task_status_history(db=db, task_id=task_id)

        # 获取当前允许的操作
        allowed_actions = TaskManagementService.get_task_allowed_actions(db=db, task_id=task_id)

        return {
            "task_id": task_id,
            "current_status": task.status.value,
            "status_history": status_history,
            "allowed_actions": allowed_actions,
            "total_transitions": len(status_history)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("get_task_status_history_error", task_id=task_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务状态历史失败"
        )


@router.get("/{task_id}/results")
async def get_task_results(
    task_id: int,
    db: Session = Depends(get_db)
):
    """
    获取任务结果
    """
    try:
        # 获取任务
        task = crud_task.get(db=db, id=task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务 {task_id} 不存在"
            )

        # 获取任务的所有批次
        batches = crud_batch.get_by_task_id(db=db, task_id=task_id)

        # 收集所有结果文件
        result_files = []
        for batch in batches:
            # TODO: 获取批次的结果文件 (需要在后续任务中实现Result模型的查询)
            # 目前返回批次中的源文件信息作为占位
            files = crud_file.get_by_batch_id(db=db, batch_id=batch.id)
            for file in files:
                if file.status == FileStatus.PROCESSED:
                    result_files.append({
                        "id": file.id,
                        "name": file.filename,
                        "original_name": file.original_filename,
                        "file_type": file.file_type.value,
                        "size": file.file_size,
                        "storage_path": file.storage_path,
                        "download_url": f"/api/files/download/{file.id}",
                        "batch_id": batch.id,
                        "batch_name": batch.name
                    })

        return {
            "task_id": task_id,
            "task_name": task.name,
            "status": task.status.value,
            "progress": task.progress,
            "total_batches": task.total_batches,
            "completed_batches": task.completed_batches,
            "result_files": result_files,
            "total_results": len(result_files)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("get_task_results_error", task_id=task_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务结果失败"
        )


@router.websocket("/{task_id}/ws")
async def websocket_endpoint(websocket: WebSocket, task_id: str):
    """
    任务进度WebSocket连接
    """
    await manager.connect(websocket, task_id)
    try:
        while True:
            # 保持连接活跃
            data = await websocket.receive_text()
            # 可以处理客户端发送的消息
    except WebSocketDisconnect:
        manager.disconnect(websocket, task_id)


@router.get("/types")
async def get_task_types():
    """
    获取所有支持的任务类型和参数模式

    Returns:
        任务类型字典，包含每种类型的参数模式和默认值
    """
    task_types = {}

    for task_type in TaskType:
        task_types[task_type.value] = {
            "name": task_type.value,
            "display_name": _get_task_display_name(task_type),
            "category": _get_task_category(task_type),
            "parameter_schema": ParameterValidator.get_parameter_schema(task_type),
            "default_parameters": ParameterValidator.get_default_parameters(task_type),
            "description": _get_task_description(task_type)
        }

    return {
        "task_types": task_types,
        "queue_types": [qt.value for qt in QueueType],
        "supported_file_types": {
            "image": ["jpg", "jpeg", "png", "gif", "bmp", "tiff", "webp"],
            "video": ["mp4", "avi", "mov", "mkv", "wmv", "flv"]
        }
    }


@router.get("/types/{task_type}/schema")
async def get_task_parameter_schema(task_type: TaskType):
    """
    获取特定任务类型的参数模式

    Args:
        task_type: 任务类型

    Returns:
        参数模式和默认值
    """
    return {
        "task_type": task_type.value,
        "parameter_schema": ParameterValidator.get_parameter_schema(task_type),
        "default_parameters": ParameterValidator.get_default_parameters(task_type),
        "complexity_info": _get_complexity_info(task_type)
    }


@router.get("/statistics")
async def get_tasks_statistics(
    task_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """
    获取任务统计信息

    Args:
        task_id: 可选的任务ID，如果提供则返回单个任务统计，否则返回全局统计
        db: 数据库会话

    Returns:
        统计信息
    """
    try:
        statistics = TaskPersistenceService.get_task_statistics(db=db, task_id=task_id)

        return {
            "statistics": statistics,
            "task_id": task_id,
            "timestamp": datetime.now().isoformat()
        }

    except TaskPersistenceError as e:
        logger.error("get_statistics_error", task_id=task_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error("get_statistics_unexpected_error", task_id=task_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计信息失败"
        )


@router.post("/{task_id}/update-progress")
async def update_task_progress(
    task_id: int,
    db: Session = Depends(get_db)
):
    """
    手动更新任务进度

    Args:
        task_id: 任务ID
        db: 数据库会话

    Returns:
        更新后的任务信息
    """
    try:
        updated_task = TaskPersistenceService.update_task_progress(db=db, task_id=task_id)

        if not updated_task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务 {task_id} 不存在"
            )

        return {
            "message": f"任务 {task_id} 进度已更新",
            "task_id": task_id,
            "progress": updated_task.progress,
            "status": updated_task.status.value,
            "completed_batches": updated_task.completed_batches,
            "failed_batches": updated_task.failed_batches
        }

    except TaskPersistenceError as e:
        logger.error("update_progress_error", task_id=task_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error("update_progress_unexpected_error", task_id=task_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新任务进度失败"
        )


def _get_task_display_name(task_type: TaskType) -> str:
    """获取任务类型的显示名称"""
    display_names = {
        TaskType.IMAGE_SHARPEN: "图像锐化",
        TaskType.IMAGE_GRAYSCALE: "图像灰度转换",
        TaskType.IMAGE_EDGE_DETECTION: "图像边缘检测",
        TaskType.IMAGE_GAMMA_CORRECTION: "图像伽马校正",
        TaskType.IMAGE_FUSION: "图像融合",
        TaskType.IMAGE_STITCHING: "图像拼接",
        TaskType.BEAUTY_ENHANCEMENT: "美颜处理",
        TaskType.TEXTURE_TRANSFER: "纹理转换",

        TaskType.VIDEO_RESIZE: "视频缩放",
        TaskType.VIDEO_GRAYSCALE: "视频灰度转换",
        TaskType.VIDEO_EXTRACT_FRAME: "视频帧提取",
        TaskType.VIDEO_EDGE_DETECTION: "视频边缘检测",
        TaskType.VIDEO_BLUR: "视频模糊",
        TaskType.VIDEO_BINARY: "视频二值化",
        TaskType.VIDEO_TRANSFORM: "视频变换",
        TaskType.VIDEO_THUMBNAIL: "视频缩略图",
    }
    return display_names.get(task_type, task_type.value)


def _get_task_category(task_type: TaskType) -> str:
    """获取任务类型的分类"""
    if task_type.value.startswith("image_"):
        return "image"
    elif task_type.value.startswith("video_"):
        return "video"
    else:
        return "other"


def _get_task_description(task_type: TaskType) -> str:
    """获取任务类型的描述"""
    descriptions = {
        TaskType.IMAGE_SHARPEN: "增强图像的清晰度和细节",
        TaskType.IMAGE_GRAYSCALE: "将彩色图像转换为灰度图像",
        TaskType.IMAGE_EDGE_DETECTION: "检测图像中的边缘和轮廓",
        TaskType.IMAGE_GAMMA_CORRECTION: "调整图像的亮度和对比度",
        TaskType.IMAGE_FUSION: "将两张图像融合为一张",
        TaskType.IMAGE_STITCHING: "将多张图像拼接成全景图",
        TaskType.BEAUTY_ENHANCEMENT: "美颜处理，包括磨皮和瘦脸",
        TaskType.TEXTURE_TRANSFER: "将一张图像的纹理转移到另一张图像",

        TaskType.VIDEO_RESIZE: "调整视频的分辨率和尺寸",
        TaskType.VIDEO_GRAYSCALE: "将彩色视频转换为灰度视频",
        TaskType.VIDEO_EXTRACT_FRAME: "从视频中提取指定帧",
        TaskType.VIDEO_EDGE_DETECTION: "对视频进行边缘检测处理",
        TaskType.VIDEO_BLUR: "对视频应用模糊效果",
        TaskType.VIDEO_BINARY: "将视频转换为二值化效果",
        TaskType.VIDEO_TRANSFORM: "对视频进行旋转、翻转等变换",
        TaskType.VIDEO_THUMBNAIL: "生成视频缩略图",
    }
    return descriptions.get(task_type, "")


def _get_complexity_info(task_type: TaskType) -> Dict[str, Any]:
    """获取任务复杂度信息"""
    complexity_info = {
        "base_complexity": "medium",
        "factors": [],
        "estimated_time_per_mb": 0.5
    }

    # 低复杂度任务
    low_complexity_tasks = {
        TaskType.IMAGE_GRAYSCALE, TaskType.VIDEO_THUMBNAIL,
        TaskType.VIDEO_EXTRACT_FRAME
    }

    # 高复杂度任务
    high_complexity_tasks = {
        TaskType.IMAGE_STITCHING, TaskType.TEXTURE_TRANSFER,
        TaskType.VIDEO_EDGE_DETECTION
    }

    if task_type in low_complexity_tasks:
        complexity_info["base_complexity"] = "low"
        complexity_info["estimated_time_per_mb"] = 0.2
        complexity_info["factors"].append("快速处理")
    elif task_type in high_complexity_tasks:
        complexity_info["base_complexity"] = "high"
        complexity_info["estimated_time_per_mb"] = 1.5
        complexity_info["factors"].append("复杂算法")

    return complexity_info
