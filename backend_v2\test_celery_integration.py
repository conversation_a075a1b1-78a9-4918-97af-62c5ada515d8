#!/usr/bin/env python3
"""
测试Celery集成功能
验证2.1.3的实现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.celery_app import celery_app
from app.services.task_dispatcher import TaskDispatcher, TaskDispatchError
from app.schemas.task_submit import TaskType, QueueType

def test_celery_app_configuration():
    """测试Celery应用配置"""
    print("🧪 测试Celery应用配置...")
    
    # 验证Celery应用实例
    assert celery_app is not None, "Celery应用实例不存在"
    assert celery_app.main == "clover_tasks", "Celery应用名称不正确"
    
    # 验证配置
    assert celery_app.conf.task_serializer == "json", "任务序列化配置不正确"
    assert celery_app.conf.result_serializer == "json", "结果序列化配置不正确"
    assert celery_app.conf.timezone == "UTC", "时区配置不正确"
    
    print("✅ Celery应用配置验证通过")

def test_task_routing_configuration():
    """测试任务路由配置"""
    print("\n🧪 测试任务路由配置...")
    
    # 验证任务路由映射
    task_routes = celery_app.conf.task_routes
    
    # 测试CPU图像处理任务路由
    cpu_image_tasks = [
        "app.tasks.image_tasks.process_image_sharpen",
        "app.tasks.image_tasks.process_image_grayscale",
        "app.tasks.image_tasks.process_image_edge_detection",
        "app.tasks.image_tasks.process_image_gamma_correction"
    ]

    for task_name in cpu_image_tasks:
        assert task_name in task_routes, f"任务 {task_name} 路由配置缺失"
        assert task_routes[task_name]["queue"] == "cpu", f"任务 {task_name} 队列配置不正确"

    # 测试GPU任务路由
    gpu_tasks = [
        "app.tasks.image_tasks.process_image_fusion",
        "app.tasks.image_tasks.process_image_stitching",
        "app.tasks.image_tasks.process_beauty_enhancement",
        "app.tasks.image_tasks.process_texture_transfer",
        "app.tasks.video_tasks.process_video_resize",
        "app.tasks.video_tasks.process_video_edge_detection",
        "app.tasks.video_tasks.process_video_transform"
    ]

    for task_name in gpu_tasks:
        assert task_name in task_routes, f"任务 {task_name} 路由配置缺失"
        assert task_routes[task_name]["queue"] == "gpu", f"任务 {task_name} 队列配置不正确"
    
    print("✅ 任务路由配置验证通过")

def test_queue_configuration():
    """测试队列配置"""
    print("\n🧪 测试队列配置...")
    
    # 验证队列配置
    task_queues = celery_app.conf.task_queues
    expected_queues = ["cpu", "gpu", "io", "hybrid"]
    
    for queue_name in expected_queues:
        assert queue_name in task_queues, f"队列 {queue_name} 配置缺失"
        queue_config = task_queues[queue_name]
        assert queue_config["exchange"] == queue_name, f"队列 {queue_name} exchange配置不正确"
        assert queue_config["routing_key"] == queue_name, f"队列 {queue_name} routing_key配置不正确"
    
    print("✅ 队列配置验证通过")

def test_task_dispatcher_mapping():
    """测试任务分发器映射"""
    print("\n🧪 测试任务分发器映射...")
    
    # 验证任务类型映射
    task_mapping = TaskDispatcher.TASK_TYPE_MAPPING
    
    # 测试所有任务类型都有映射
    for task_type in TaskType:
        assert task_type in task_mapping, f"任务类型 {task_type} 缺少映射"
        celery_task_name = task_mapping[task_type]
        assert celery_task_name.startswith("app.tasks."), f"任务名称格式不正确: {celery_task_name}"
    
    # 验证队列映射
    queue_mapping = TaskDispatcher.QUEUE_MAPPING
    for queue_type in QueueType:
        assert queue_type in queue_mapping, f"队列类型 {queue_type} 缺少映射"
        queue_name = queue_mapping[queue_type]
        assert queue_name in ["cpu", "gpu", "io", "hybrid"], f"队列名称不正确: {queue_name}"
    
    print("✅ 任务分发器映射验证通过")

def test_health_check_task():
    """测试健康检查任务"""
    print("\n🧪 测试健康检查任务...")
    
    try:
        # 测试健康检查任务是否可以调用
        from app.core.celery_app import health_check
        
        # 验证任务函数存在
        assert callable(health_check), "健康检查任务不可调用"
        
        # 模拟调用（不实际执行）
        print("✅ 健康检查任务验证通过")
        
    except Exception as e:
        print(f"❌ 健康检查任务验证失败: {e}")
        raise

def test_task_dispatcher_methods():
    """测试任务分发器方法"""
    print("\n🧪 测试任务分发器方法...")
    
    # 验证TaskDispatcher类方法存在
    required_methods = [
        "dispatch_task",
        "get_task_status", 
        "cancel_task",
        "get_queue_status"
    ]
    
    for method_name in required_methods:
        assert hasattr(TaskDispatcher, method_name), f"TaskDispatcher缺少方法: {method_name}"
        method = getattr(TaskDispatcher, method_name)
        assert callable(method), f"方法 {method_name} 不可调用"
    
    print("✅ 任务分发器方法验证通过")

def test_batch_task_structure():
    """测试批次任务结构"""
    print("\n🧪 测试批次任务结构...")
    
    try:
        from app.tasks.batch_tasks import process_batch, monitor_batch_progress
        
        # 验证批次处理任务
        assert callable(process_batch), "process_batch任务不可调用"
        assert callable(monitor_batch_progress), "monitor_batch_progress任务不可调用"
        
        # 验证任务名称
        assert process_batch.name == "app.tasks.batch_tasks.process_batch", "process_batch任务名称不正确"
        assert monitor_batch_progress.name == "app.tasks.batch_tasks.monitor_batch_progress", "monitor_batch_progress任务名称不正确"
        
        print("✅ 批次任务结构验证通过")
        
    except Exception as e:
        print(f"❌ 批次任务结构验证失败: {e}")
        raise

def test_image_video_tasks():
    """测试图像和视频任务模块"""
    print("\n🧪 测试图像和视频任务模块...")
    
    try:
        # 测试图像任务
        from app.tasks import image_tasks
        image_task_functions = [
            "process_image_sharpen",
            "process_image_grayscale",
            "process_image_edge_detection",
            "process_image_gamma_correction",
            "process_image_fusion",
            "process_image_stitching",
            "process_beauty_enhancement",
            "process_texture_transfer"
        ]
        
        for func_name in image_task_functions:
            assert hasattr(image_tasks, func_name), f"图像任务缺少函数: {func_name}"
            func = getattr(image_tasks, func_name)
            assert callable(func), f"图像任务函数 {func_name} 不可调用"
        
        # 测试视频任务
        from app.tasks import video_tasks
        video_task_functions = [
            "process_video_resize",
            "process_video_grayscale",
            "process_video_extract_frame",
            "process_video_edge_detection",
            "process_video_blur",
            "process_video_binary",
            "process_video_transform",
            "process_video_thumbnail"
        ]
        
        for func_name in video_task_functions:
            assert hasattr(video_tasks, func_name), f"视频任务缺少函数: {func_name}"
            func = getattr(video_tasks, func_name)
            assert callable(func), f"视频任务函数 {func_name} 不可调用"
        
        print("✅ 图像和视频任务模块验证通过")
        
    except Exception as e:
        print(f"❌ 图像和视频任务模块验证失败: {e}")
        raise

def test_configuration_completeness():
    """测试配置完整性"""
    print("\n🧪 测试配置完整性...")
    
    # 验证所有TaskType都有对应的Celery任务
    task_mapping = TaskDispatcher.TASK_TYPE_MAPPING
    
    for task_type in TaskType:
        celery_task_name = task_mapping[task_type]
        
        # 验证任务在路由配置中
        assert celery_task_name in celery_app.conf.task_routes, f"任务 {celery_task_name} 缺少路由配置"
        
        # 验证队列配置
        queue_name = celery_app.conf.task_routes[celery_task_name]["queue"]
        assert queue_name in celery_app.conf.task_queues, f"队列 {queue_name} 配置缺失"
    
    print("✅ 配置完整性验证通过")

if __name__ == "__main__":
    print("🚀 开始测试任务2.1.3的Celery集成...")
    
    try:
        # 运行所有测试
        test_celery_app_configuration()
        test_task_routing_configuration()
        test_queue_configuration()
        test_task_dispatcher_mapping()
        test_health_check_task()
        test_task_dispatcher_methods()
        test_batch_task_structure()
        test_image_video_tasks()
        test_configuration_completeness()
        
        print("\n🎉 所有测试通过！")
        print("✅ 任务2.1.3 - 集成Celery任务队列系统 验证完成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
